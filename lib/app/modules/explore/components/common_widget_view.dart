import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/profile_view/controllers/profile_view_controller.dart';
import 'package:incenti_ai/app/modules/project_detail_view/controllers/project_detail_view_controller.dart';
import 'package:incenti_ai/app/modules/sub_project_detail_view/controllers/sub_project_detail_view_controller.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import 'package:incenti_ai/utillites/typography.dart';
import 'package:intl/intl.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../main.dart';
import '../../../../models/app_post_model.dart';
import '../../../../services/app_link_service.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/delete_confirmation_dialog.dart';
import '../../../../utillites/network_image.dart';
import '../../../routes/app_pages.dart';
import '../../communities/controllers/communities_controller.dart';
import '../../projects/controllers/projects_controller.dart';
import '../../user_detail/components/image_picker_bottom_sheet.dart';
ProjectDetailViewController get projectController =>
    Get.find<ProjectDetailViewController>(tag: Get.arguments['projectId'].toString());
bool get isProjectRegister =>
    Get.isRegistered<ProjectDetailViewController>(tag: Get.arguments['projectId'].toString());

Row reactionView({
  required VoidCallback onLike,
  required VoidCallback onLikeLongPress,
  required VoidCallback onComment,
  required VoidCallback onBookmark,
  required Function()? onShare,
  required bool isLiked,
  required int likesCount,
  required bool isBookmarked,
  required int commentsCount,
  required int UserId,
  required int postId,
}) {
  return Row(
    // mainAxisAlignment: MainAxisAlignment.center,
    children: [
      InkWell(
        onTap: () {
          if (!isLiked) {
            HapticFeedback.lightImpact();
          }
          onLike();
        },
        onLongPress: () {
          HapticFeedback.lightImpact();
          onLikeLongPress();
        },
        child: Container(
          padding: EdgeInsets.all(5),
          child: Row(
            children: [
              commonIconsView(
                onTap: () {
                  if (!isLiked) {
                    HapticFeedback.lightImpact();
                  }
                  onLike();
                },
                padding: const EdgeInsets.all(0),
                image: isLiked ? AppImage.fillHeart : AppImage.heartIcon,
                height: MySize.getScaledSizeHeight(20),
                width: MySize.getScaledSizeWidth(20),
              ),
              if (likesCount != 0)
                Padding(
                  padding: const EdgeInsets.only(left: 3),
                  child: TypoGraphy(
                    text: "$likesCount",
                    textStyle: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w400,
                      fontFamily: "Inter",
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
      InkWell(
        onTap: onComment,
        child: Container(
          child: Row(
            children: [
              commonIconsView(
                onTap: onComment,
                padding: EdgeInsets.only(left: MySize.getScaledSizeWidth(15)),
                image: AppImage.commentIcon,
              ),
              if (commentsCount != 0)
                Padding(
                  padding: const EdgeInsets.only(left: 3),
                  child: TypoGraphy(
                    text: "$commentsCount",
                    textStyle: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w400,
                      fontFamily: "Inter",
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
      Space.width(20),
      commonIconsView(
          onTap: onShare,
          padding: const EdgeInsets.all(0),
          image: AppImage.share,
          color: Color(0xFF787e89)),
      Spacer(),
      if(UserId == 411) ...[
        InkWell(
          onTap: () {
            Get.toNamed(Routes.post, arguments: {"PostId": postId});
          },
          child: Row(
            children: [
              Space.width(30),
              SvgPicture.asset("assets/svg/repost_icon.svg",
                height: MySize.getScaledSizeHeight(28), width: MySize.size24,),
            ],
          ),
        ),
        Space.width(5),
      ],
      commonIconsView(
        onTap: onBookmark,
        padding: const EdgeInsets.all(0),
        image: isBookmarked ? AppImage.fillBookMark : AppImage.bookmarkIcon,
      ),
    ],
  );
}

Widget commonIconsView({
  Function()? onTap,
  String? image,
  double? height,
  double? width,
  Color? color,
  required EdgeInsetsGeometry padding,
}) {
  return Padding(
    padding: padding,
    child: GestureDetector(
      onTap: onTap,
      child: SvgPicture.asset(
        image!,
        height: height ?? MySize.getScaledSizeHeight(24),
        color: color,
      ),
    ),
  );
}

Row feedHeadView(Post res, BuildContext context,
    {bool isImagePreview = false,
    bool isUser = false,
    required RxBool isShare,
      void Function()? blockCallBack,
    bool isBookMarkDuplicate = true,bool isPostEditUser = false,void Function()? onFeedTap,void Function()? onRepost,bool isExplore = false,bool isProfile = false}) {
  return Row(
    crossAxisAlignment: (res.project != null || res.community != null)
        ? CrossAxisAlignment.start
        : CrossAxisAlignment.center,
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      InkWell(
        onTap: () {
          if (res.user?.id == CurrentUser.user.id) {
            Get.toNamed(Routes.profile);
          } else {
            Get.toNamed(Routes.other_user_profile,
                arguments: {"UserId": res.user?.id,"isNeedBottom": true});
          }
        },
        child: Container(
          height: MySize.getScaledSizeHeight(42),
          width: MySize.getScaledSizeWidth(42),
          margin: EdgeInsets.symmetric(
            vertical: MySize.getScaledSizeHeight(3),
            horizontal: MySize.getScaledSizeWidth(3),
          ),
          child: profileImage(
              userName: res.user?.firstName ?? "",
              url: res.user?.image ?? "",
              width: MySize.getScaledSizeWidth(42),
              height: MySize.getScaledSizeHeight(42),
              borderColor: Colors.transparent,
              color: AppTheme.darkGrey[100]),
        ),
      ),
      SizedBox(width: MySize.getScaledSizeWidth(12)),
      Expanded(
        child: GestureDetector(
          onTap: () {
            if (res.user?.id == CurrentUser.user.id) {
              Get.toNamed(Routes.profile);
            } else {
              Get.toNamed(Routes.other_user_profile,
                  arguments: {"UserId": res.user?.id});
            }
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TypoGraphy(
                          text: "${res.user?.firstName} ${res.user?.lastName}",
                          level: 4,
                          // color: isImagePreview
                          //     ? AppTheme.white
                          //     : AppTheme.white,
                        ),
                        if (res.project != null) ...[
                          GestureDetector(
                            onTap: () {
                              Get.toNamed(Routes.project_detail, arguments: {
                                "projectId": res.project?.parentProjectData != null
                                    ? res.project?.parentProjectData?.id
                                    : res.project?.id,
                              });
                            },
                            child: Padding(
                              padding: EdgeInsets.only(
                                top: MySize.getScaledSizeHeight(4),
                                bottom: MySize.getScaledSizeHeight(6.5),
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    height: MySize.getScaledSizeHeight(18),
                                    width: MySize.getScaledSizeWidth(18),
                                    margin: EdgeInsets.only(
                                      right: MySize.getScaledSizeWidth(5),
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(200),
                                      child: (res.project?.parentProjectData
                                                      ?.image ??
                                                  res.project?.image) !=
                                              ""
                                          ? NetworkImageComponent(
                                              imageUrl: res
                                                      .project
                                                      ?.parentProjectData
                                                      ?.image ??
                                                  res.project?.image,
                                              simmerHeight:
                                                  MySize.getScaledSizeHeight(76),
                                              width:
                                                  MySize.getScaledSizeWidth(90),
                                            )
                                          : Image.asset(
                                              AppImage.defaultImage,
                                              width:
                                                  MySize.getScaledSizeWidth(90),
                                              height:
                                                  MySize.getScaledSizeHeight(76),
                                            ),
                                    ),
                                  ),
                                  TypoGraphy(
                                    text: res.project?.parentProjectData?.name ??
                                        res.project?.name ??
                                        '',
                                    level: 2,
                                    fontWeight: FontWeight.w600,
                                    // color: isImagePreview
                                    //     ? AppTheme.white
                                    //     : AppTheme.white,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                        if(res.community != null) ...[
                          GestureDetector(
                            onTap: () {
                              Get.put(CommunitiesController());
                              Get.toNamed(Routes.community_detail,arguments: {'communityId': res.community?.id});
                            },
                            child: Padding(
                              padding: EdgeInsets.only(
                                top: MySize.getScaledSizeHeight(4),
                                bottom: MySize.getScaledSizeHeight(6.5),
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    height: MySize.getScaledSizeHeight(18),
                                    width: MySize.getScaledSizeWidth(18),
                                    margin: EdgeInsets.only(
                                      right: MySize.getScaledSizeWidth(5),
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(50),
                                      child: (res.community?.image ?? "").isNotEmpty
                                          ? NetworkImageComponent(
                                        imageUrl: res.community?.image,
                                        simmerHeight:
                                        MySize.getScaledSizeHeight(76),
                                        width:
                                        MySize.getScaledSizeWidth(90),
                                      )
                                          : Image.asset(
                                        AppImage.defaultCommunity,
                                        width:
                                        MySize.getScaledSizeWidth(90),
                                        height:
                                        MySize.getScaledSizeHeight(76),
                                      ),
                                    ),
                                  ),
                                  TypoGraphy(
                                    text: res.community?.name ?? '',
                                    level: 2,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ]
                      ],
                    ),
                  ),
                ],
              ),
              TypoGraphy(
                text: DateFormat('MMM d, y')
                    .format(DateTime.parse(res.createdAt.toString())),
                level: 2,
                fontWeight: FontWeight.w400,
                color: isImagePreview ? AppTheme.grey : AppTheme.grey,
              )
            ],
          ),
        ),
      ),
      if ((res.pinnedAt?? '').isNotEmpty && res.user?.id == CurrentUser.user.id)
      ClipRRect(
        borderRadius: BorderRadius.circular(50),
        child: BackdropFilter(
          filter: ImageFilter.blur(
              sigmaX: 30, sigmaY: 30),
          child: Container(
            color: box.read('isDarkMode') ? AppTheme.white.withOpacity(0.2) : AppTheme.black.withOpacity(0.2),
            height: MySize.getScaledSizeHeight(38),
            width: MySize.getScaledSizeWidth(38),
            child: Padding(
              padding: const EdgeInsets.all(9),
              child: SvgPicture.asset(
                AppImage.pinPost,
                color: AppTheme.white,
              ),
            ),
          ),
        ),
      ),
      Space.width(10),
      GestureDetector(
        onTap: onFeedTap ?? () {
          HapticFeedback.lightImpact();
          if (isImagePreview) {
            Get.back();
          } else if (isUser) {
            if (res.user?.id == CurrentUser.user.id) {
              ImagePickerBottomSheet.show(
                  context: context,
                  child:
                      showUserPostOption(postId: res.id, isPin: res.pinnedAt,postSlug: res.slug,isProfile: isProfile, isShare: isShare));
            } else {
              ImagePickerBottomSheet.show(
                  context: context,
                  child: showPostOption(isDuplicate: false, postId: res.id,onTap: onRepost,title: res.title,postSlug: res.slug, isShare: isShare));
            }
          } else {
            if(isPostEditUser) {
              ImagePickerBottomSheet.show(
                  context: context,
                  child: showProjectPostEditOption(
                      postId: res.id,
                      postSlug: res.slug,
                    onTap: onRepost,
                  title: res.title, isShare: isShare
                  ));
            } else {
              ImagePickerBottomSheet.show(
                  context: context,
                  child: showPostOption(
                      isDuplicate: !isBookMarkDuplicate ? false : true,
                      postId: res.id,
                      postSlug: res.slug,
                      onTap: onRepost,
                      blockCallBack: blockCallBack,
                      title: res.title,
                      isExplore: isExplore, isShare: isShare
                  ),
              );
            }
          }
        },
        child: isImagePreview
            ? Container(
                height: MySize.size40,
                width: MySize.size40,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppTheme.white.withValues(alpha: 0.15),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(10),
                  child: SvgPicture.asset(
                    AppImage.closeImage,
                    color: AppTheme.white,
                    height: MySize.getScaledSizeHeight(16.33),
                    width: MySize.getScaledSizeWidth(16.33),
                  ),
                ),
              )
            : SvgPicture.asset(
                AppImage.moreVertIcon,
                height: MySize.getScaledSizeWidth(24),
                width: MySize.getScaledSizeHeight(24),
                color: Color(0xFF787E89),
              ),
      ),
    ],
  );
}

Row draftHeadView(Post res, BuildContext context,
    {bool isImagePreview = false, required ProfileViewController controller}) {
  return Row(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      TypoGraphy(
        text:
            "Edited on ${DateFormat('MMM d, y').format(DateTime.parse(res.updatedAt.toString()))}",
        level: 2,
        fontWeight: FontWeight.w400,
        color: Color(0xFF787E89),
      ),
      Spacer(),
      GestureDetector(
        onTap: () {
          HapticFeedback.lightImpact();
          ImagePickerBottomSheet.show(
              context: context,
              child: showDuplicatePostOption(
                  postId: res.id, controller: controller));
        },
        child: SvgPicture.asset(
          AppImage.moreVertIcon,
          height: MySize.getScaledSizeWidth(24),
          width: MySize.getScaledSizeHeight(24),
          color: Color(0xFF787E89),
        ),
      ),
    ],
  );
}

Column showPostOption({
  bool isDuplicate = false,
  String? postSlug,
  int? postId,
  int? projectId,
  String? projectSlug,
  bool isProject = false,
  bool isSubProject = false,
  void Function()? onTap,
  void Function()? blockCallBack,
  String title = '',
  bool isExplore = false,
  required RxBool isShare,
  void Function(String)? onShareContentType, // Callback to set content type
}) {
  return Column(
    children: [
      Space.height(40),
      /*if (isDuplicate) ...[
        InkWell(
          onTap: () {
            Get.toNamed(Routes.post, arguments: {"PostId": postId});
          },
          child: Row(
            children: [
              Space.width(30),
              SvgPicture.asset(AppImage.duplicateDraft,
                  height: MySize.size24, width: MySize.size24,color: AppTheme.whiteWithNull,),
              Space.width(20),
              TypoGraphy(
                text: "Copy to Edit",
                level: 5,
              )
            ],
          ),
        ),
        Space.height(41),
      ],*/
      InkWell(
        onTap: (){
          if(isProject){
            HapticFeedback.lightImpact();
            isShare.value = true;
            onShareContentType?.call("project");
            Navigator.pop(Get.context!);
            AppLinkService().shareMedia(slug: projectSlug ?? '', mediaType: ShareMediaType.projects,title: title);
          }else {
            HapticFeedback.lightImpact();
            isShare.value = true;
            // Set content type for post sharing
            onShareContentType?.call("post");
            Navigator.pop(Get.context!);
            AppLinkService().shareMedia(slug: postSlug ?? '',
                mediaType: ShareMediaType.posts,
                title: title);
          }
        },
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(AppImage.share,
                height: MySize.size24, width: MySize.size24,color: AppTheme.whiteWithNull),
            Space.width(20),
            TypoGraphy(
              text: "Share",
              level: 5,
            )
          ],
        ),
      ),
      Space.height(41),
      InkWell(
        onTap: onTap,
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(AppImage.report,
                height: MySize.size24, width: MySize.size24,color: AppTheme.whiteWithNull),
            Space.width(20),
            TypoGraphy(
              text: isProject ? "Report this Project" : "Report this Post",
              level: 5,
            )
          ],
        ),
      ),
      if(isExplore) ...[
        Space.height(41),
        InkWell(
          onTap: blockCallBack,
          child: Row(
            children: [
              Space.width(30),
              Image.asset(AppImage.block,
                  height: MySize.size24, width: MySize.size24),
              Space.width(20),
              TypoGraphy(
                text: "Block User",
                level: 5,
                color: AppTheme.red,
              )
            ],
          ),
        ),
      ],
      Space.height(61),
    ],
  );
}

Column showProjectPostEditOption({
  int? postId,
  String? postSlug,
  void Function()? onTap,
  String? title,
  required RxBool isShare,
  void Function(String)? onShareContentType, // Callback to set content type
}) {
  return Column(
    children: [
      Space.height(40),
      InkWell(
        onTap: () {
          Get.toNamed(Routes.post,
              arguments: {"PostId": postId, "isPostEdit": true})?.then(
                (value) {
              Get.back();
              if (isProjectRegister) {
                projectController
                    .page.value = 1;
                projectController
                    .hasMoreData.value = true;
                projectController.postDataList.clear();
                projectController.callApiForUserGetPost(
                  context: Get.context!,
                );
              }
              if (Get.isRegistered<SubProjectDetailViewController>()) {
                Get.find<SubProjectDetailViewController>()
                    .page.value = 1;
                Get.find<SubProjectDetailViewController>()
                    .hasMoreData.value = true;
                Get.find<SubProjectDetailViewController>().postDataList.clear();
                Get.find<SubProjectDetailViewController>()
                    .callApiForUserGetPost(
                  context: Get.context!,
                );
              }
              final pvc = Get.isRegistered<ProfileViewController>()
                  ? Get.find<ProfileViewController>()
                  : Get.put(ProfileViewController());
              pvc.hasMoreData.value = true;
              pvc.page.value = 1;
              pvc.postDataList.clear();
              pvc.callApiForUserGetPost(
                context: Get.context!,
              );
            },
          );
        },
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(
              AppImage.editIcon,
              height: MySize.size24,
              width: MySize.size24,
              color: AppTheme.whiteWithBase,
            ),
            Space.width(20),
            TypoGraphy(
              text: "Edit",
              level: 5,
            )
          ],
        ),
      ),
      Space.height(40),
      InkWell(
        onTap: (){
          HapticFeedback.lightImpact();
          isShare.value = true;
          // Set content type for post sharing
          onShareContentType?.call("post");
          Navigator.pop(Get.context!);
          AppLinkService().shareMedia(slug: postSlug ?? '', mediaType: ShareMediaType.posts,title: title);
        },
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(AppImage.share,
                height: MySize.size24, width: MySize.size24,color: AppTheme.whiteWithBase),
            Space.width(20),
            TypoGraphy(
              text: "Share",
              level: 5,
            )
          ],
        ),
      ),
      Space.height(41),
      InkWell(
        onTap: onTap,
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(AppImage.report,
                height: MySize.size24, width: MySize.size24,color: AppTheme.whiteWithBase),
            Space.width(20),
            TypoGraphy(
              text: "Report this Post",
              level: 5,
            )
          ],
        ),
      ),
      Space.height(61),
    ],
  );
}

Column showPostDetailOption({bool isUser = false,void Function()? callBack, void Function()? onTap,void Function()? onShareTap,void Function()? reportPostOnTap,void Function()? deleteOnTap,int? postId}) {
  return Column(
    children: [
      Space.height(40),
      if (isUser) ...[
        InkWell(
          onTap: onTap ?? () {},
          child: Row(
            children: [
              Space.width(30),
              SvgPicture.asset(
                AppImage.editIcon,
                height: MySize.size24,
                width: MySize.size24,
                color: AppTheme.whiteWithBase,
              ),
              Space.width(20),
              TypoGraphy(
                text: "Edit",
                level: 5,
              )
            ],
          ),
        ),
        Space.height(41),
        InkWell(
          onTap: () {
            Navigator.pop(Get.context!);
            Get.toNamed(Routes.move_project, arguments: {"postId": postId})?.then(
                  (value) {
                if(callBack != null) {
                  callBack();
                }
              },
            );
          },
          child: Row(
            children: [
              Space.width(30),
              SvgPicture.asset(
                AppImage.movePost,
                height: MySize.size24,
                width: MySize.size24,
                color: AppTheme.whiteWithNull,
              ),
              Space.width(20),
              TypoGraphy(
                text: "Move Post to",
                level: 5,
              )
            ],
          ),
        ),
        Space.height(41),
      ],
      InkWell(
        onTap: onShareTap,
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(AppImage.share,
                height: MySize.size24, width: MySize.size24,color: AppTheme.whiteWithNull,),
            Space.width(20),
            TypoGraphy(
              text: "Share",
              level: 5,
            )
          ],
        ),
      ),
      if (!isUser) ...[
        Space.height(41),
        InkWell(
          onTap: reportPostOnTap,
          child: Row(
            children: [
              Space.width(30),
              SvgPicture.asset(AppImage.report,
                  height: MySize.size24, width: MySize.size24,color: AppTheme.whiteWithNull,),
              Space.width(20),
              TypoGraphy(
                text: "Report this Post",
                level: 5,
              )
            ],
          ),
        ),
      ],
      if (isUser) ...[
        Space.height(41),
        InkWell(
          onTap: deleteOnTap ?? () {},
          child: Row(
            children: [
              Space.width(30),
              SvgPicture.asset(
                AppImage.trashIcon,
                height: MySize.size24,
                width: MySize.size24,
                color: AppTheme.red,
              ),
              Space.width(20),
              TypoGraphy(
                text: "Delete",
                level: 5,
                color: AppTheme.red,
              )
            ],
          ),
        ),
      ],
      Space.height(61),
    ],
  );
}

Column showDuplicatePostOption(
    {int? postId, required ProfileViewController controller}) {
  return Column(
    children: [
      Space.height(40),
      InkWell(
        onTap: () {
          Get.toNamed(Routes.post,
              arguments: {"PostId": postId, "isDraft": true,"isProfile": true})?.then(
            (value) async {
              Get.back();
              controller.page.value = 1;
              controller.hasMoreData.value = true;
              controller.draftDataList.clear();
              await controller.callApiForUserGetPost(
                context: Get.context!,
                isPublished: true,
              );
            },
          );
        },
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(
              AppImage.editIcon,
              height: MySize.size24,
              width: MySize.size24,
              color: AppTheme.whiteWithBase,
            ),
            Space.width(20),
            TypoGraphy(
              text: "Edit",
              level: 5,
            )
          ],
        ),
      ),
      Space.height(41),
      InkWell(
        onTap: () async {
          Navigator.pop(Get.context!);
          HapticFeedback.heavyImpact();
          showDeleteConfirmationDialog(
            context: Get.context!,
            description: "Are you sure you want to delete draft permanently.",
            onConfirm: () async {
              await controller
                  .callApiForDeleteOnePost(
                      context: Get.context!, postId: postId, isDraft: true)
                  .then(
                (value) {
                  controller
                      .page.value = 1;
                  controller
                      .hasMoreData.value = true;
                  controller.draftDataList.clear();
                  controller.callApiForUserGetPost(
                      context: Get.context!, isPublished: true);
                },
              );
            },
            title: "Delete Draft",
            onCancel: () {
              Get.back();
            }, isLoading: controller.isLoading,
          );
        },
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(
              AppImage.trashIcon,
              height: MySize.size24,
              width: MySize.size24,
              color: AppTheme.red,
            ),
            Space.width(20),
            TypoGraphy(
              text: "Delete",
              level: 5,
              color: AppTheme.red,
            )
          ],
        ),
      ),
      Space.height(61),
    ],
  );
}

Column showUserPostOption({required RxBool isShare,bool? isBack = false, int? postId, String? isPin ,String? title,bool isSubProjectEdit = false,bool isProfile = false,bool isProjectEdit = false,String? postSlug, void Function(String)? onShareContentType}) {
  return Column(
    children: [
      Space.height(40),
      InkWell(
        onTap: (){
          HapticFeedback.lightImpact();
          isShare.value = true;
          // Set content type for post sharing
          onShareContentType?.call("post");
          Navigator.pop(Get.context!);
          AppLinkService().shareMedia(slug: postSlug ?? '', mediaType: ShareMediaType.posts,title: title);
        },
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(AppImage.share,
                height: MySize.size24, width: MySize.size24,color: AppTheme.whiteWithNull,),
            Space.width(20),
            TypoGraphy(
              text: "Share",
              level: 5,
            )
          ],
        ),
      ),
      Space.height(41),
      InkWell(
        onTap: () async {
          final pvc = Get.isRegistered<ProfileViewController>()
              ? Get.find<ProfileViewController>()
              : Get.put(ProfileViewController());
          await pvc
              .callApiForPinPost(context: Get.context!, postId: postId)
              .then(
            (value) {
              if (isProjectRegister) {
                projectController.page.value = 1;
                projectController.hasMoreData.value = true;
                projectController.postDataList.clear();
                projectController.callApiForUserGetPost(
                  context: Get.context!,
                );
              }
              if (Get.isRegistered<SubProjectDetailViewController>()) {
                Get.find<SubProjectDetailViewController>().page.value = 1;
                Get.find<SubProjectDetailViewController>().hasMoreData.value = true;
                Get.find<SubProjectDetailViewController>().postDataList.clear();
                Get.find<SubProjectDetailViewController>()
                    .callApiForUserGetPost(
                  context: Get.context!,
                );
              }
              pvc.hasMoreData.value = true;
              pvc.page.value = 1;
              pvc.postDataList.clear();
              if(pvc.currentSelectedIndex.value == 3) {
                pvc.BookMarkList.clear();
                pvc.callApiForGetBookMarkProject(
                  context: Get.context!,
                );
              }
              pvc.callApiForUserGetPost(
                context: Get.context!,
              );
            },
          );
        },
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(
              AppImage.pinPost,
              height: MySize.size24,
              width: MySize.size24,
              color: AppTheme.whiteWithNull,
            ),
            Space.width(20),
            TypoGraphy(
              text: isPin == "" ? "Pin Post" : "Unpin Post",
              level: 5,
            )
          ],
        ),
      ),
      Space.height(41),
      InkWell(
        onTap: () {
          print("post rout ==>");
          if(isBack ?? false){
            Get.back();
          }
          Get.toNamed(Routes.post,
              arguments: {"PostId": postId, "isPostEdit": true,"isCrossVisible": false,"isSubProjectEdit": isSubProjectEdit,"isProfile": isProfile,"isProjectEdit": isProjectEdit})?.then(
            (value) {
              Get.back();
              if (isProjectRegister) {
                projectController.page.value = 1;
                projectController.hasMoreData.value = true;
                projectController.postDataList.clear();
                projectController.callApiForUserGetPost(
                  context: Get.context!,
                );
              }
              if (Get.isRegistered<SubProjectDetailViewController>()) {
                Get.find<SubProjectDetailViewController>().page.value = 1;
                Get.find<SubProjectDetailViewController>().hasMoreData.value = true;
                Get.find<SubProjectDetailViewController>().postDataList.clear();
                Get.find<SubProjectDetailViewController>()
                    .callApiForUserGetPost(
                  context: Get.context!,
                );
              }
              final pvc = Get.isRegistered<ProfileViewController>()
                  ? Get.find<ProfileViewController>()
                  : Get.put(ProfileViewController());
              pvc.hasMoreData.value = true;
              pvc.page.value = 1;
              pvc.postDataList.clear();
              pvc.callApiForUserGetPost(
                context: Get.context!,
              );
            },
          );
        },
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(
              AppImage.editIcon,
              height: MySize.size24,
              width: MySize.size24,
              color: AppTheme.whiteWithBase,
            ),
            Space.width(20),
            TypoGraphy(
              text: "Edit",
              level: 5,
            )
          ],
        ),
      ),
      Space.height(41),
      InkWell(
        onTap: () {
          Navigator.pop(Get.context!);
          Get.toNamed(Routes.move_project, arguments: {"postId": postId})?.then(
            (value) {
              if (Get.isRegistered<SubProjectDetailViewController>()) {
                Get.find<SubProjectDetailViewController>().page.value = 1;
                Get.find<SubProjectDetailViewController>().hasMoreData.value = true;
                Get.find<SubProjectDetailViewController>().postDataList.clear();
                Get.find<SubProjectDetailViewController>()
                    .callApiForUserGetPost(
                  context: Get.context!,
                );
              }
              if (Get.isRegistered<ProfileViewController>()) {
                Get.find<ProfileViewController>().hasMoreData.value = true;
                Get.find<ProfileViewController>().page.value = 1;
                Get.find<ProfileViewController>().postDataList.clear();
                Get.find<ProfileViewController>()
                    .callApiForUserGetPost(context: Get.context!);
              }
              if (isProjectRegister) {
                projectController.page.value = 1;
                projectController.hasMoreData.value = true;
                projectController.postDataList.clear();
                projectController
                    .callApiForUserGetPost(context: Get.context!);
              }
            },
          );
        },
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(
              AppImage.movePost,
              height: MySize.size24,
              width: MySize.size24,
              color: AppTheme.whiteWithNull,
            ),
            Space.width(20),
            TypoGraphy(
              text: "Move Post to",
              level: 5,
            )
          ],
        ),
      ),
      Space.height(41),
      InkWell(
        onTap: () async {
          Navigator.pop(Get.context!);
          HapticFeedback.heavyImpact();
          final pvc = Get.isRegistered<ProfileViewController>()
              ? Get.find<ProfileViewController>()
              : Get.put(ProfileViewController());
          showDeleteConfirmationDialog(
            context: Get.context!,
            description: "Are you sure you want to delete Post permanently?",
            onConfirm: () async {
              await pvc
                  .callApiForDeleteOnePost(
                      context: Get.context!, postId: postId)
                  .then(
                (value) {
                  pvc.hasMoreData.value = true;
                  pvc.page.value = 1;
                  pvc.callApiForUserGetPost(context: Get.context!);
                  if (isProjectRegister) {
                    projectController.page.value = 1;
                    projectController.hasMoreData.value = true;
                    projectController
                        .postDataList
                        .clear();
                    projectController
                        .callApiForUserGetPost(
                      context: Get.context!,
                    );
                  }
                  if (Get.isRegistered<SubProjectDetailViewController>()) {
                    Get.find<SubProjectDetailViewController>().page.value = 1;
                    Get.find<SubProjectDetailViewController>().hasMoreData.value = true;
                    Get.find<SubProjectDetailViewController>()
                        .postDataList
                        .clear();
                    Get.find<SubProjectDetailViewController>()
                        .callApiForUserGetPost(
                      context: Get.context!,
                    );
                  }
                },
              );
            },
            title: "Delete Post",
            onCancel: () {
              Get.back();
            }, isLoading: pvc.isLoading,
          );
        },
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(
              AppImage.trashIcon,
              height: MySize.size24,
              width: MySize.size24,
              color: AppTheme.red,
            ),
            Space.width(20),
            TypoGraphy(
              text: "Delete",
              level: 5,
              color: AppTheme.red,
            )
          ],
        ),
      ),
      Space.height(50),
    ],
  );
}

Column showPostEditOption(
    {String? projectId,String? projectSlug,
    String? projectName,
      int? userId,
    bool isProjectEdit = false,
    bool isPrivate = false,
    required RxBool isShare,
    bool isNotDelete = false,
    void Function()? hidePrivate,
    bool isHide = false,
      void Function()? onTap,
      void Function()? onTapDeleteConfirm,
      void Function(String)? onShareContentType, // Callback to set content type
    }) {
  return Column(
    children: [
      Space.height(40),
      InkWell(
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
        onTap: onTap ?? () {
          Navigator.pop(Get.context!);
          Get.toNamed(Routes.create_project,
              arguments: {"projectId": projectId, "isEdit": true})?.then(
            (value) {
              if (isProjectEdit) {
                // Get.back();
                Get.find<ProjectDetailViewController>(tag: projectId).callApiForGetOneProject(
                    context: Get.context!, projectId: projectId.toString(),shouldReload: true).then((value) {
                      if(Get.find<ProjectDetailViewController>(tag: projectId).currentSelectedIndex.value == 1) {
                        Get.find<ProjectDetailViewController>(tag: projectId).callApiForOneSubProject(
                            context: Get.context!,
                            projectId: projectId.toString(),userId: userId);
                      }
                    },);
                if (Get.isRegistered<SubProjectDetailViewController>(tag: projectId)) {
                  Get.find<SubProjectDetailViewController>(tag: projectId)
                      .callApiForGetOneProject(
                          context: Get.context!,
                          projectId: projectId.toString());
                }
              } else {
                Get.find<ProjectsController>().page.value = 1;
                Get.find<ProjectsController>().hasMoreData.value = true;
                Get.find<ProjectsController>().createdProject.clear();
                // Get.find<ProjectsController>().isLoadingCreated.value = true;
                Get.find<ProjectsController>().callApiForProjectData(
                    context: Get.context!, isSelected: true);
              }
            },
          );
        },
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(
              AppImage.editIcon,
              height: MySize.size24,
              width: MySize.size24,
              color: AppTheme.whiteWithBase,
            ),
            Space.width(20),
            TypoGraphy(
              text: "Edit",
              level: 5,
            )
          ],
        ),
      ),
      if (isPrivate) ...[
        Space.height(41),
        InkWell(
          highlightColor: Colors.transparent,
          splashColor: Colors.transparent,
          onTap: () {
            Navigator.pop(Get.context!);
            Get.dialog(
              BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Dialog(
                  insetPadding:
                      EdgeInsets.symmetric(horizontal: MySize.size20 ?? 20),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(30.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Space.height(20),
                        // Title
                        TypoGraphy(
                          text: "${!isHide ? "Hide Private" : "Undo Hide"} Project",
                          level: 12,
                          textAlign: TextAlign.center,
                        ),
                        Space.height(10),

                        // Description
                        TypoGraphy(
                          text: isHide ? "Currently others can’t see your Private Project $projectName. Do you want to undo that? " :
                              "Currently others are able to see your Private Project $projectName. Do you want to hide that?",
                          level: 3,
                          color: AppTheme.grey,
                          textAlign: TextAlign.center,
                        ),
                        Space.height(40),
                        // Buttons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            // Cancel Button
                            Expanded(
                              child: InkWell(
                                highlightColor: Colors.transparent,
                                splashColor: Colors.transparent,
                                onTap: () => Get.back(),
                                child: Container(
                                  height: MySize.getScaledSizeHeight(70),
                                  width: MySize.getScaledSizeHeight(170),
                                  alignment: Alignment.center,
                                  child: TypoGraphy(
                                    text: "No",
                                    level: 4,
                                  ),
                                ),
                              ),
                            ),

                            SizedBox(width: 10),

                            // Confirm Delete Button
                            Expanded(
                              child: Buttons(
                                buttonText: "Yes",
                                buttonTextLevel: 4,
                                color: AppTheme.red,
                                height: MySize.getScaledSizeHeight(70),
                                onTap: () {
                                  HapticFeedback.heavyImpact();
                                  hidePrivate!(); // Execute Delete Action
                                },
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
          child: Row(
            children: [
              Space.width(30),
              Icon(CupertinoIcons.lock_fill,
                  size: MySize.getScaledSizeHeight(20)),
              Space.width(20),
              TypoGraphy(
                text:
                    isHide ? "Undo Hide Project" : "Hide Private Project",
                level: 5,
              )
            ],
          ),
        ),
      ],
      Space.height(41),
      if(isNotDelete)
        InkWell(
          onTap: (){
            HapticFeedback.lightImpact();
            isShare.value = true;
            // Set content type for main project sharing
            onShareContentType?.call("project");
            Navigator.pop(Get.context!);
            AppLinkService().shareMedia(slug: projectSlug ?? '', mediaType: ShareMediaType.projects,title: projectName);
          },
          child: Row(
            children: [
              Space.width(30),
              SvgPicture.asset(AppImage.share,
                  height: MySize.size24, width: MySize.size24,color: AppTheme.whiteWithNull,),
              Space.width(20),
              TypoGraphy(
                text: "Share",
                level: 5,
              )
            ],
          ),
        ),
      if (!isNotDelete)
        InkWell(
          onTap: () {
            Navigator.pop(Get.context!);
            HapticFeedback.heavyImpact();
            showDeleteConfirmationDialog(
              context: Get.context!,
              description:
                  "Are you sure you want to delete Project permanently?",
              onConfirm: onTapDeleteConfirm ?? () async {
                Get.back();
                if (isProjectEdit) {
                  projectController
                      .callApiForDeleteOneProject(
                          context: Get.context!, projectId: projectId,isBack: true)
                      .then(
                    (value) {
                      // Get.back();
                      // controller.callApiForOneSubProject(
                      //     context: Get.context!,
                      //     projectId: controller.projectId.value
                      //         .toString(),
                      //     userId: controller
                      //         .getProjectDetailData
                      //         .value
                      //         .userId);
                      if (isProjectRegister) {
                        projectController
                            .callApiForOneSubProject(
                          context: Get.context!,
                          projectId: projectController
                              .projectId
                              .value
                              .toString(),
                          userId: CurrentUser.user.id,
                        );
                      }
                      if (!Get.isRegistered<ProjectsController>()) {
                        Get.put(ProjectsController());
                      }
                      Get.find<ProjectsController>().page.value = 1;
                      Get.find<ProjectsController>().hasMoreData.value = true;
                      Get.find<ProjectsController>().createdProject.clear();
                      Get.find<ProjectsController>().callApiForProjectData(
                        context: Get.context!,
                        isSelected: true,
                      );
                    },
                  );
                } else {
                  Get.find<ProjectsController>().page.value = 1;
                  Get.find<ProjectsController>().hasMoreData.value = true;
                  Get.find<ProjectsController>().createdProject.clear();
                  Get.find<ProjectsController>().callApiForDeleteOneProject(
                      context: Get.context!, projectId: projectId);
                }
              },
              title: "Delete Project",
              onCancel: () {
                Get.back();
              }, isLoading: isProjectEdit ? projectController.isLoading : Get.find<ProjectsController>().isLoading,
            );
          },
          child: Row(
            children: [
              Space.width(30),
              SvgPicture.asset(AppImage.trashIcon,
                  height: MySize.size24, width: MySize.size24),
              Space.width(20),
              TypoGraphy(
                text: "Delete",
                level: 5,
                color: AppTheme.red,
              )
            ],
          ),
        ),
      Space.height(61),
    ],
  );
}

Widget storyContainer(
    {required String imageUrl, required String name, void Function()? onTap}) {
  return Expanded(
    child: GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        Get.back();
        onTap != null ? onTap() : null;
      },
      child: Container(
        alignment: Alignment.center,
        height: MySize.getScaledSizeHeight(75),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(MySize.size14 ?? 25),
          color: box.read('isDarkMode') ? AppTheme.bottomBar : AppTheme.subPrimary,
          border: Border.all(
            color: AppTheme.borderWithTrans
          )
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              imageUrl,
              height: MySize.getScaledSizeHeight(24),
              width: MySize.getScaledSizeWidth(24),
              color: box.read('isDarkMode') ? Colors.white : AppTheme.baseBlack,
            ),
            Space.height(5),
            TypoGraphy(
              text: name,
              level: 3,
              fontWeight: FontWeight.w500,
            ),
          ],
        ),
      ),
    ),
  );
}

showBackDialogue({
  required BuildContext context,
}) {
  showDialog(
      context: context,
      builder: (context) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: Dialog(
            insetPadding:
                EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(70)),
            backgroundColor: Color(0xff25282d),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: Padding(
              padding: EdgeInsets.all(MySize.getScaledSizeHeight(20))
                  .copyWith(top: MySize.getScaledSizeHeight(30)),
              child: Column(
                spacing: MySize.getScaledSizeHeight(10),
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  TypoGraphy(
                    text: 'Discard edits?',
                    level: 4,
                    fontWeight: FontWeight.w600,
                    color: Color(0xfff2f4f4),
                  ),
                  TypoGraphy(
                    textAlign: TextAlign.center,
                    text:
                        'If you go back now, you\'ll loose all the edits you\'ve made.',
                    level: 4,
                    fontWeight: FontWeight.w400,
                    color: Color(0xfff2f4f4),
                  ),
                  Space.height(15),
                  InkWell(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      Get.back();
                      Get.back();
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TypoGraphy(
                            textAlign: TextAlign.center,
                            text: 'Discard',
                            level: 3,
                            fontWeight: FontWeight.w500,
                            color: Color(0xfff56475),
                          ),
                        ],
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      Get.back();
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TypoGraphy(
                            textAlign: TextAlign.center,
                            text: 'Keep editing',
                            level: 3,
                            fontWeight: FontWeight.w500,
                            color: Color(0xfff2f4f4),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      });
}
