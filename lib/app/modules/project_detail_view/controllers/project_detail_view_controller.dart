import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/project_detail_view/components/project_detail_sections/other_user_members_section.dart';
import 'package:incenti_ai/app/modules/project_detail_view/components/project_detail_sections/posts_section.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/current_user.dart';

import '../../../../constants/api.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/constant.dart';
import '../../../../models/app_comment_model.dart';
import '../../../../models/app_post_model.dart';
import '../../../../models/app_project_model.dart';

import '../../../../models/app_todo_model.dart';
import '../../../../models/get_all_highlights_model.dart';

import '../../../../models/app_project_team_member_model.dart';
import '../../../../models/post_like_model.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/common_function.dart';
import '../components/project_detail_sections/drafts_section.dart';
import '../components/project_detail_sections/files_section.dart';
import '../components/project_detail_sections/members_section.dart';
import '../../../../models/section_base.dart';
import '../components/project_detail_sections/sub_projects_section.dart';
import '../components/project_detail_sections/todos_section.dart';

class ProjectDetailViewController extends GetxController with WidgetsBindingObserver {
  List<SectionBase> sections = [];

  RxBool isLoading = false.obs;
  RxBool isHighlightLoading = false.obs;
  RxBool isUserLoading = false.obs;
  RxBool isUserSubProjectLoading = false.obs;
  RxBool isUserPostLoading = false.obs;
  RxBool isFoldersLoading = false.obs;
  RxBool isProjectFollow = false.obs;
  RxBool isProjectNotFound = false.obs;
  ApiManager apiManager = ApiManager();
  Rx<Project> getProjectDetailData = Project().obs;
  final ScrollController scrollController = ScrollController();
  var args = Get.arguments;
  RxList<PostLikeData> postLikeList = <PostLikeData>[].obs;
  RxInt currentSelectedIndex = 0.obs;
  RxInt selectedIndex = 0.obs;
  RxInt filterSelectedIndex = 0.obs;
  RxBool isButtonClick = false.obs;
  var selectedPriorities = <String>[].obs;
  RxList priority = ["Low", "Medium", "High"].obs;
  RxBool isFilterApply = false.obs;
  RxList<Post> postDataList = <Post>[].obs;
  RxList<ProjectTeamMemberData> teamMemberDataList =
      <ProjectTeamMemberData>[].obs;
  RxBool isShare = false.obs;
  RxList<SubProjectData> subProjectList = <SubProjectData>[].obs;
  TextEditingController folderNameController = TextEditingController();
  RxList<FolderWrapper> folderList = <FolderWrapper>[].obs;
  int limit = 4;
  RxInt page = 1.obs;
  RxBool hasMoreData = true.obs;
  RxInt projectId = (-1).obs;
  RxString projectSlug = ('').obs;
  RxList<Comment> commentDataList = <Comment>[].obs;
  TextEditingController commentController = TextEditingController();
  FocusNode commentFocusNode = FocusNode();
  RxList<Highlight> highlights = <Highlight>[].obs;
  RxBool isFollowing = false.obs;
  RxList<ToDo> todoList = <ToDo>[].obs;
  final downloadingFileIds = <int>{}.obs;
  RxString selectedReason = "".obs;
  RxList repostData = [
    "I just don't Like it",
    "Scam, fraud or spam",
    "False information",
  ].obs;

  @override
  void onInit() {
    // TODO: implement onInit
    WidgetsBinding.instance.addObserver(this);

    super.onInit();
    if (args != null && args["projectSlug"] != null) {
      projectSlug.value = args["projectSlug"] ?? '';
    }
    if (args != null && args["projectId"] != null) {
      projectId.value = args["projectId"];
    }

    var parameter = projectSlug.value.isNotEmpty
        ? projectSlug.value
        : projectId.value.toString();

    callApiForGetOneProject(context: Get.context!, projectId: parameter).then(
      (value) {
        callApiForUserGetPost(context: Get.context!);
        callApiForGetHighlightsOfProject(context: Get.context!);

        if (CurrentUser.user.id == getProjectDetailData.value.userData?.id ||
            (getProjectDetailData.value.projectMembers.isNotEmpty)) {
          sections = [
            PostsSection(controller: this),
            SubProjectsSection(controller: this),
            TodosSection(controller: this),
            FilesSection(controller: this),
            MembersSection(controller: this),
            DraftsSection(controller: this),
          ];
        } else {
          sections = [
            PostsSection(controller: this),
            SubProjectsSection(controller: this),
            OtherUserMembersSection(controller: this),
          ];
        }
      },
    );
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if(isShare.value) {
      if (state == AppLifecycleState.resumed) {
        isShare.value = false;
        CommonFunction.showCustomSnackbar(
            message: "The ${currentSelectedIndex.value == 1 ? "sub-project" : "project"} has been shared successfully");
      }
      return;
    }
  }

  callApiForReportProject(
      {required BuildContext context, required String projectId}) {
    FocusScope.of(context).unfocus();
    // isLoading.value = true;
    final ApiModel updatePost =
        ApiModel("/reported-projects/project/$projectId", APIType.POST);
    log("selected ==> ${selectedReason.value}");
    return apiManager.callApi(
      updatePost,
      params: {"reason": selectedReason.value},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            Navigator.pop(context);
            CommonFunction.showCustomSnackbar(message: response['message']);
          }
        } catch (error) {}
      },
      failureCallback: (message, statusCode) {},
    );
  }


  callApiForGetPostLike(
      {required BuildContext context, required int postId}) async {
    isLoading.value = true;
    FocusScope.of(context).unfocus();
    String? lastCreatedAt = postLikeList.isNotEmpty
        ? postLikeList.last.createdAt?.toIso8601String()
        : null;

    try {
      await apiManager.callApi(
        ApiModel("/post-likes/post/$postId", APIType.GET),
        params: {
          "limit": 20,
          if (lastCreatedAt != null) "createdAt": lastCreatedAt
        },
        successCallback: (response, message) async {
          if (response['status'] == 'success') {
            PostLike postLike = PostLike.fromJson(response);
            if ((postLike.data?.data?.length ?? 0) < 20) {
              hasMoreData.value = false;
            }
            if ((postLike.data?.data ?? []).isNotEmpty) {
              postLikeList.addAll(postLike.data?.data ?? []);
              postLikeList.refresh();
            }
          }
          isLoading.value = false;
        },
        failureCallback: (message, statusCode) {
          hasMoreData.value = false;
          isLoading.value = false;
        },
      );
    } catch (error) {
      hasMoreData.value = false;
      isLoading.value = false;
    }
  }

  callApiForReportPost(
      {required BuildContext context, required String postId}) {
    FocusScope.of(context).unfocus();
    // isLoading.value = true;
    final ApiModel updatePost =
        ApiModel("/reported-posts/post/$postId", APIType.POST);
    log("selected ==> ${selectedReason.value}");
    return apiManager.callApi(
      updatePost,
      params: {"reason": selectedReason.value},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommonFunction.showCustomSnackbar(message: response['message']);
            page.value = 1;
            hasMoreData.value = true;
            postDataList.clear();
            callApiForUserGetPost(context: Get.context!);
          }
        } catch (error) {}
      },
      failureCallback: (message, statusCode) {},
    );
  }

  Future<void> callApiForCompleteToDo(
      {required BuildContext context,
      required String todoId,
      required int index}) {
    FocusScope.of(context).unfocus();
    todoList[index].status?.value = !(todoList[index].status?.value ?? false);
    final ApiModel getPost = ApiModel("/todos/status/$todoId", APIType.PATCH);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
          }
        } catch (error) {
          log("error === $error");
        }
      },
      failureCallback: (message, statusCode) {
        CommonFunction.showCustomSnackbar(
            message: statusCode, isError: true, backgroundColor: AppTheme.red);
        todoList[index].status?.value =
            !(todoList[index].status?.value ?? false);
      },
    );
  }

  Future<void> callApiForToDo({required BuildContext context}) async {
    log('api called for =====> todo');
    isLoading.value = true;

    try {
      Map<String, dynamic> dict = {
        "limit": limit,
        "page": page.value,
        "ProjectIds": projectId.value
      };
      log("dict==$dict");
      await apiManager.callApi(
        APIS.todo.getTodo,
        params: dict,
        successCallback: (response, message) async {
          if (response['status'] == 'success') {
            TodoResponse todoResponse = TodoResponse.fromJson(response);
            if (todoResponse.data.data.length < limit) {
              hasMoreData.value = false;
            }
            if (page.value == 1) {
              todoList.clear();
            }
            todoList.addAll(todoResponse.data.data);
            page++;
          }
        },
        failureCallback: (message, statusCode) {
          hasMoreData.value = false;
          isLoading.value = false;
        },
      );
    } catch (error) {
      hasMoreData.value = false;
    } finally {
      isLoading.value = false;
    }
  }

  final iconMap = {
    1: AppImage.projectIcon,
    2: AppImage.todosIcon,
    3: AppImage.projectIcon,
    4: AppImage.invite,
  };

  Future<void> callApiForHideProject(
      {required BuildContext context,
      String? projectId,
      required bool isHide,
      bool isSubProject = false,
      int index = 0}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost = ApiModel("/projects/$projectId", APIType.PATCH);
    log('api called for =====> hide project');
    return apiManager.callApi(
      getPost,
      params: {"hide": isHide},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            if (isSubProject) {
              subProjectList[index].hide = !(subProjectList[index].hide);
              Get.back();
              if (subProjectList[index].hide == true) {
                CommonFunction.showCustomSnackbar(
                    message:
                        "Other users won't be able to view this Private Project Title and Cover Image");
              } else {
                CommonFunction.showCustomSnackbar(
                    message:
                        "Other Users are now able to view your Project Title and Cover Image");
              }
            } else {
              getProjectDetailData.value.hide =
                  !(getProjectDetailData.value.hide ?? false);
              Get.back();
              if (getProjectDetailData.value.hide == true) {
                CommonFunction.showCustomSnackbar(
                    message:
                        "Other users won't be able to view this Private Project Title and Cover Image");
              } else {
                CommonFunction.showCustomSnackbar(
                    message:
                        "Other Users are now able to view your Project Title and Cover Image");
              }
            }

            log("response === $response");
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForProjectMemberRole(
      {required BuildContext context,
      String? projectId,
      int index = 0,
      required String access}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost =
        ApiModel("/project-members/$projectId", APIType.PATCH);
    log('api called for =====> ProjectMemberRole');
    return apiManager.callApi(
      getPost,
      params: {"access": access},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            //  else {
            //   getProjectDetailData.value.hide =
            //   !(getProjectDetailData.value.hide ?? false);
            // }
            // Get.back();
            Navigator.pop(context);
            CommonFunction.showCustomSnackbar(message: response['message']);
            log("response === $response");
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForGetOneProject(
      {required BuildContext context,
      required String projectId,
      bool shouldReload = true}) {
    FocusScope.of(context).unfocus();
    if (shouldReload) isUserLoading.value = true;
    final ApiModel getPost = ApiModel("/projects/$projectId", APIType.GET);
    log('api called for =====> get one project');

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            if(response['data'] == null) {
              isProjectNotFound.value = true;
              if (shouldReload) isUserLoading.value = false;
              return;
            }
            getProjectDetailData.value = Project.fromJson(response["data"]);
            if (response["data"]["isFollowed"] == "1") {
              isProjectFollow.value = true;
            } else {
              isProjectFollow.value = false;
            }
            if (shouldReload) isUserLoading.value = false;
          }
        } catch (error) {
          if (shouldReload) isUserLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        if(statusCode == "Project not found") {
          isProjectNotFound.value = true;
        }
        if (shouldReload) isUserLoading.value = false;
      },
    );
  }

  Future<void> callApiForGetProjectTeamMember({required BuildContext context}) {
    isLoading.value = true;
    final ApiModel getProjectMember =
        ApiModel("/project-members/projects/$projectId", APIType.GET);
    log('api called for =====> project team member');

    return apiManager.callApi(
      getProjectMember,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            // Parse the response and update the list
            ProjectTeamMemberResponse parsedResponse =
                ProjectTeamMemberResponse.fromJson(response);
            teamMemberDataList.value = parsedResponse.data;
            isLoading.value = false;
          }
        } catch (error) {
          hasMoreData.value = false;
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        hasMoreData.value = false;
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForProjectFollow(
      {required BuildContext context, required String projectId}) {
    FocusScope.of(context).unfocus();
    // isLoading.value = true;
    final ApiModel updatePost =
        ApiModel("/project-followers/project/${projectId}", APIType.POST);
    isProjectFollow.value = true;
    log('api called for =====> project follow');

    return apiManager.callApi(
      updatePost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            // callApiForGetOneProject(
            //     context: Get.context!, projectId: projectId);
            isProjectFollow.value = true;
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
            // isLoading.value = false;
          }
        } catch (error) {
          isProjectFollow.value = false;
          // isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        // isLoading.value = false;
        isProjectFollow.value = false;
      },
    );
  }

  Future<void> callApiForProjectUnFollow(
      {required BuildContext context, required String projectId}) {
    FocusScope.of(context).unfocus();
    // isLoading.value = true;
    final ApiModel updatePost =
        ApiModel("/project-followers/project/${projectId}", APIType.DELETE);
    isProjectFollow.value = false;
    log('api called for =====> project unfollow');

    return apiManager.callApi(
      updatePost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            // callApiForGetOneProject(
            //     context: Get.context!, projectId: projectId);
            isProjectFollow.value = false;
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
            // isLoading.value = false;
          }
        } catch (error) {
          isProjectFollow.value = true;
          // isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        // isLoading.value = false;
        isProjectFollow.value = true;
      },
    );
  }

  Future<void> callApiForDeleteTeamMember(
      {required BuildContext context,
      required int? teamId,
      bool isLeaveTeam = false,required int userId}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel updatePost =
        ApiModel("/project-members/$teamId/$userId", APIType.DELETE);
    return apiManager.callApi(
      updatePost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            isLoading.value = false;
            if (isLeaveTeam) {
              Get.back();
              CommonFunction.showCustomSnackbar(
                message: "Project left successfully",
              );
            } else {
              CommonFunction.showCustomSnackbar(
                message: response['message'],
              );
            }
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForDeleteOneToDo(
      {required BuildContext context, String? todoId}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost = ApiModel("/todos/$todoId", APIType.DELETE);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            Navigator.pop(context);
            CommonFunction.showCustomSnackbar(message: response['message']);
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForOneSubProject(
      {required BuildContext context, String? projectId, int? userId}) {
    FocusScope.of(context).unfocus();
    isUserSubProjectLoading.value = true;
    log('api called for =====> one sub project');

    return apiManager.callApi(
      APIS.project.getAllMyProject,
      params: {"UserId": userId, "ParentId": projectId},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            subProjectList.value = List<SubProjectData>.from(response["data"]
                    ["data"]
                .map((x) => SubProjectData.fromJson(x)));
            await callApiForGetOneProject(
                context: context,
                projectId: projectId.toString(),
                shouldReload: false);
            isUserSubProjectLoading.value = false;
          }
        } catch (error) {
          isUserSubProjectLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isUserSubProjectLoading.value = false;
      },
    );
  }

  Future<void> callApiForDeleteOnePost(
      {required BuildContext context, int? postId, bool isDraft = false}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost = ApiModel("/posts/$postId", APIType.DELETE);
    log('api called for =====> delete one post');

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            Get.back();
            // Clear the list and refresh it
            postDataList.clear();
            update();
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForUserGetPost(
      {required BuildContext context, bool isPublished = false}) {
    FocusScope.of(context).unfocus();
    isUserPostLoading.value = true;
    // String? lastCreatedAt = postDataList.isNotEmpty
    //     ? postDataList.last.createdAt
    //         ?.toIso8601String() // Convert DateTime to String
    //     : null;
    log('api called for =====> user get post');

    return apiManager.callApi(
      APIS.post.getAllUsePost,
      params: {
        "limit": limit,
        "page": page.value,
        // "UserId": getProjectDetailData.value.userData?.id,
        "ProjectId": projectId.value,
        if (isPublished) "isPublished": 0
      },
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            PostResponse postResponse = PostResponse.fromJson(response);
            if (postResponse.data.data.length < limit) {
              hasMoreData.value = false;
            }
            if (postResponse.data.data.isNotEmpty) {
              postDataList.addAll(postResponse.data.data);
              isUserPostLoading.value = false;
            } else {
              isUserPostLoading.value = false;
            }
            page.value++;
            log("response === $response");
          }
        } catch (error) {
          isUserPostLoading.value = false;
          hasMoreData.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isUserPostLoading.value = false;
        hasMoreData.value = false;
      },
    );
  }

  callApiForLikeProject(
      {required BuildContext context, String? postId, required int index}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    postDataList[index].isLiked?.value =
        !(postDataList[index].isLiked?.value ?? false);
    if (postDataList[index].isLiked?.value == true) {
      postDataList[index].likesCount?.value++;
    } else {
      postDataList[index].likesCount?.value--;
    }
    final ApiModel getPost = ApiModel("/post-likes/post/$postId", APIType.POST);
    log('api called for =====> for like project');

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            // postDataList[index].isLiked = (response["post"]["isLiked"] == "0" ? false : true).obs;
            // postDataList[index].likesCount = int.parse(response["post"]["likesCount"] ?? "0");
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForGetFolders({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    isFoldersLoading.value = true;
    log('api called for =====> get folders');

    return apiManager.callApi(
      APIS.folders.getAllFolders,
      params: {"ProjectId": projectId},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            folderList.value = List<ProjectFolder>.from(
                response["data"]["data"].map((x) => ProjectFolder.fromJson(x)));
            isFoldersLoading.value = false;
          }
        } catch (error) {
          isFoldersLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isFoldersLoading.value = false;
      },
    );
  }

  Future<void> callApiForFollowUser(
      {required BuildContext context, required int? userId}) {
    log('api called for =====> follow user');

    FocusScope.of(context).unfocus();
    final ApiModel updatePost =
        ApiModel("/friends/follow/user/$userId", APIType.POST);
    isFollowing.value = true;
    return apiManager.callApi(
      updatePost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            isFollowing.value = true;
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
          }
        } catch (error) {
          isFollowing.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isFollowing.value = false;
      },
    );
  }

  Future<void> callApiForUnFollowUser(
      {required BuildContext context,
      required int? userId,
      required bool isUnFollowed,
      required int index}) {
    FocusScope.of(context).unfocus();
    final ApiModel updatePost =
        ApiModel("/friends/user/$userId", APIType.DELETE);
    isFollowing.value = false;
    log('api called for =====> unfollow user');

    return apiManager.callApi(
      updatePost,
      params: {if (isUnFollowed) "unfollow": 1},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            isFollowing.value = false;
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
            teamMemberDataList[index].user?.isFollowing?.value = true;
            Navigator.pop(context);
          }
        } catch (error) {
          isFollowing.value = true;
        }
      },
      failureCallback: (message, statusCode) {
        isFollowing.value = true;
      },
    );
  }

  Future<void> callApiForCreateFolder({
    required BuildContext context,
    required String folderName,
  }) async {
    log('api called for =====> create folder');

    FocusScope.of(context).unfocus();
    Get.back();
    folderNameController.clear();
    isFoldersLoading.value = true;
    apiManager.callApi(
      APIS.folders.createFolder,
      params: {
        'ProjectId': projectId.toString(),
        'name': folderName,
      },
      successCallback: (response, message) {
        callApiForGetFolders(
          context: Get.context!,
        );
      },
      failureCallback: (message, statusCode) {
        CommonFunction.showCustomSnackbar(
          message: statusCode,
          isError: true,
          backgroundColor: AppTheme.red,
        );
        isFoldersLoading.value = false;
      },
      showErrorDialog: false,
    );
  }

  Future<void> callApiForEditFolder({
    required BuildContext context,
    int? folderId,
    required String folderName,
  }) async {
    log('api called for =====> edit folder');

    FocusScope.of(context).unfocus();
    Get.back();
    folderNameController.clear();
    isFoldersLoading.value = true;
    apiManager.callApi(
      CommonFunction.prepareApi(APIS.folders.editFolder, {
        'id': folderId.toString(),
      }),
      params: {
        'name': folderName,
      },
      successCallback: (response, message) {
        callApiForGetFolders(
          context: Get.context!,
        );
      },
      failureCallback: (message, statusCode) {
        CommonFunction.showCustomSnackbar(
          message: statusCode,
          isError: true,
          backgroundColor: AppTheme.red,
        );
        isFoldersLoading.value = false;
      },
      showErrorDialog: false,
    );
  }

  Future<void> callApiForDeleteFolder({
    required int folderId,
  }) async {
    log('api called for =====> delete one folder');

    Get.back();
    isFoldersLoading.value = true;
    apiManager.callApi(
      CommonFunction.prepareApi(
        APIS.folders.deleteFolder,
        {'id': folderId.toString()},
      ),
      successCallback: (response, message) {
        callApiForGetFolders(
          context: Get.context!,
        );
      },
      failureCallback: (message, statusCode) {},
    );
  }

  callApiForGetCommentProject(
      {required BuildContext context, String? postId, required int index}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost =
        ApiModel("/post-comments/post/$postId", APIType.GET);
    log('api called for =====> get comment project');

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommentResponse commentResponse =
                CommentResponse.fromJson(response);
            commentDataList.value = commentResponse.data.data;
            postDataList[index].commentsCount?.value = commentDataList.length;
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForCommentProject(
      {required BuildContext context, String? postId, required int index}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost =
        ApiModel("/post-comments/post/$postId", APIType.POST);
    log('api called for =====> comment project');

    return apiManager.callApi(
      getPost,
      params: {"comment": commentController.value.text.trim()},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");

            isLoading.value = false;

            Comment newComment;

            if (response['data'] != null) {
              newComment = Comment.fromJson(response['data']);
            } else {
              newComment = Comment(
                id: DateTime.now().millisecondsSinceEpoch, // Temporary ID
                comment: commentController.value.text.trim(),
                createdAt: DateTime.now(),
                user: CurrentUser.user,
              );
            }

            List<Comment> updatedComments = [newComment, ...commentDataList];
            commentDataList.value = updatedComments;

            postDataList[index].commentsCount?.value = commentDataList.length;
          }
        } catch (error) {
          log("Error in callApiForCommentProject: $error");
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForBookMarkProject(
      {required BuildContext context, String? postId, required int index}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    postDataList[index].isBookMarked?.value =
        !(postDataList[index].isBookMarked?.value ?? false);
    final ApiModel getPost = ApiModel("/bookmarks/post/$postId", APIType.POST);
    log('api called for =====> bookmark project');

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            // postDataList[index].isBookMarked = response["post"]["isBookMarked"] == "0" ? false : true;
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForDeleteOneProject(
      {required BuildContext context, String? projectId,bool isBack = true,bool isSubProject = false}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost = ApiModel("/projects/$projectId", APIType.DELETE);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            if(isBack) {
              Navigator.pop(context);
            }
            if(isSubProject) {
              CommonFunction.showCustomSnackbar(
                message: "Delete Sub-Project Successfully",
              );
            } else {
              CommonFunction.showCustomSnackbar(
                message: "Delete Project Successfully",
              );
            }
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForGetHighlightsOfProject(
      {required BuildContext context}) async {
    isHighlightLoading.value = true;

    try {
      await apiManager.callApi(
        CommonFunction.prepareApi(APIS.story.getHighlights, {'id': ''}),
        params: {
          'ProjectId': projectId.value,
          'UserId': getProjectDetailData.value.userId
        },
        successCallback: (response, message) async {
          try {
            AllHighLightsModel allHighLightsModel =
                AllHighLightsModel.fromJson(response);
            highlights.value = allHighLightsModel.data?.highlights ?? [];
            // List<Future> highlightDetailsFutures = [];

            // for (Highlight highlight in highlightsList) {
            //   if (highlight.id != null) {
            //     highlightDetailsFutures.add(_getHighlightDetails(highlight.id!)
            //         .then((detailedHighlight) {
            //       if (detailedHighlight != null) {
            //         int index = highlightsList
            //             .indexWhere((h) => h.id == detailedHighlight.id);
            //         if (index != -1) {
            //           highlightsList[index] = detailedHighlight;
            //         }
            //       }
            //     }));
            //   }
            // }

            // await Future.wait(highlightDetailsFutures);

            // highlights.value = highlightsList;
            isHighlightLoading.value = false;
          } catch (error) {
            isHighlightLoading.value = false;
          }
        },
        failureCallback: (message, statusCode) {
          isHighlightLoading.value = false;
        },
      );
    } catch (e) {
      isHighlightLoading.value = false;
    }
  }

/*  Future<Highlight?> _getHighlightDetails(int highlightId) async {
    Completer<Highlight?> completer = Completer();

    apiManager.callApi(
      CommonFunction.prepareApi(
          APIS.story.getHighlights, {'id': highlightId.toString()}),
      successCallback: (response, message) {
        try {
          if (response != null &&
              response['status'] == 'success' &&
              response['data'] != null) {
            Highlight detailedHighlight = Highlight.fromJson(response['data']);

            completer.complete(detailedHighlight);
          } else {
            completer.complete(null);
          }
        } catch (error) {
          completer.complete(null);
        }
      },
      failureCallback: (message, statusCode) {
        completer.complete(null);
      },
    );

    return completer.future;
  }*/
}
