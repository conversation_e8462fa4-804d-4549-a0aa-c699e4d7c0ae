import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../constants/api.dart';
import '../../../../constants/constant.dart';
import '../../../../models/app_comment_model.dart';
import '../../../../models/app_followers_model.dart';
import '../../../../models/app_other_user_model.dart';
import '../../../../models/app_post_model.dart';
import '../../../../models/app_project_model.dart';
import '../../../../models/get_all_highlights_model.dart';
import '../../../../models/post_like_model.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/current_user.dart';


class OtherUserProfileController extends GetxController with WidgetsBindingObserver {
  RxBool isLoading = false.obs;
  RxBool isProjectLoading = false.obs;
  RxBool isProfileLoading = false.obs;
  RxBool isProfileNotFound = false.obs;
  ApiManager apiManager = ApiManager();
  var args = Get.arguments;
  Rx<OtherUser> userList = OtherUser().obs;
  RxInt currentSelectedIndex = 0.obs;
  RxInt userId = 0.obs;
  RxList<Post> postDataList = <Post>[].obs;
  int limit = 20;
  RxBool hasMoreData = true.obs;
  RxBool isFollowing = false.obs;
  RxBool isFollowUnfollow = false.obs;
  RxBool isGetPostLoading = false.obs;
  RxList<Comment> commentDataList = <Comment>[].obs;
  TextEditingController commentController = TextEditingController();
  FocusNode commentFocusNode = FocusNode();
  RxList<Project> userProject = <Project>[].obs;
  ScrollController scrollController = ScrollController();
  RxInt page = 1.obs;
  RxList<Highlight> highlights = <Highlight>[].obs;
  RxString selectedReason = "".obs;
  RxList<PostLikeData> postLikeList = <PostLikeData>[].obs;
  RxList repostData = [
    "I just don't Like it",
    "Scam, fraud or spam",
    "False information",
  ].obs;
  RxList<FollowersData> followersList = <FollowersData>[].obs;
  Rx<TextEditingController> searchController = TextEditingController().obs;
  Timer? debounceTimer;
  RxBool isShare = false.obs;

  void onScrollListener(void Function() onScroll, ScrollController controller) {
    if (!hasMoreData.value) return; // Stop calling API if no more data

    final maxScroll = controller.position.maxScrollExtent;
    if (maxScroll == controller.offset) onScroll();
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if(isShare.value) {
      if (state == AppLifecycleState.resumed) {
        isShare.value = false;
        CommonFunction.showCustomSnackbar(
            message: "The post has been shared successfully");
      }
      return;
    }
  }

  @override
  Future<void> onInit() async {
    // TODO: implement onInit
    super.onInit();
    isLoading.value = true;
    if(args != null && args["UserId"] != null) {
      userId.value = args["UserId"];
      await callApiForGetUser(context: Get.context!,userId: args["UserId"].toString()).then((value) {
        // callApiForUserGetPost(context: Get.context!,userId: args["UserId"].toString());
      },);
      await callApiForGetHighlightsOfOtherUser(context:  Get.context!);
      await callApiForUserGetPost(context: Get.context!,userId: args["UserId"].toString());
      isLoading.value = false;
    }
    scrollController.addListener(
          () => onScrollListener(() {
        page.value++;
        fetchProjectData(
            Get.context!,args["UserId"].toString());
      }, scrollController),
    );
  }

  callApiForReportProject(
      {required BuildContext context, required String projectId}) {
    FocusScope.of(context).unfocus();
    // isLoading.value = true;
    final ApiModel updatePost =
    ApiModel("/reported-projects/project/$projectId", APIType.POST);
    log("selected ==> ${selectedReason.value}");
    return apiManager.callApi(
      updatePost,
      params: {"reason": selectedReason.value},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response == > $response");
            CommonFunction.showCustomSnackbar(message: response['message']);
            page.value = 1;
            hasMoreData.value = true;
            userProject.clear();
            fetchProjectData(
                Get.context!,args["UserId"].toString());
          }
        } catch (error) {
          log("error: $message");
        }
      },
      failureCallback: (message, statusCode) {
        log("error: $message");
      },
    );
  }

  callApiForFollowersData({required BuildContext context}) {
    if (searchController.value.text.isNotEmpty) {
      // Cancel previous timer if exists
      debounceTimer?.cancel();

      // Start new debounce timer (500ms delay) for searching
      debounceTimer = Timer(Duration(milliseconds: 500), () {
        fetchFollowersData(context);
      });
    } else {
      fetchFollowersData(context);
    }
  }

  void fetchFollowersData(BuildContext context) {
    isLoading.value = true;

    apiManager.callApi(
      APIS.followers.getFollowers,
      params: {
        "UserId": userId.value,
        if (searchController.value.text.isNotEmpty)
          "searchQuery": searchController.value.text,
      },
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            FollowersResponse followersResponse =
            FollowersResponse.fromJson(response, isFollower: true);
            followersList.assignAll(followersResponse.data?.data ?? []);
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }


  callApiForReportPost(
      {required BuildContext context, required String postId}) {
    FocusScope.of(context).unfocus();
    // isLoading.value = true;
    final ApiModel updatePost =
    ApiModel("/reported-posts/post/$postId", APIType.POST);
    log("selected ==> ${selectedReason.value}");
    return apiManager.callApi(
      updatePost,
      params: {"reason": selectedReason.value},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommonFunction.showCustomSnackbar(message: response['message']);
            hasMoreData.value = true;
            page.value = 1;
            postDataList.clear();
            await callApiForUserGetPost(context: Get.context!,userId: args["UserId"].toString());
          }
        } catch (error) {
        }
      },
      failureCallback: (message, statusCode) {
      },
    );
  }

  List otherUserPostList = ["Posts", "Projects"];

  fetchProjectData(BuildContext context, String? userId) {
    if (!hasMoreData.value) return;
    isProjectLoading.value = true;

    apiManager.callApi(
      APIS.project.getAllMyProject,
      params: {
        "UserId": userId,
        'page': page.value,
        'limit': limit,
      },
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            ProjectResponse projectResponse =
            ProjectResponse.fromJson(response);
            if (projectResponse.data.data.length < limit) {
              hasMoreData.value = false;
            }
            userProject.addAll(projectResponse.data.data);
            // userProject.value = projectResponse.data.data;
            isProjectLoading.value = false;
          }
        } catch (error) {
          isProjectLoading.value = false;
          hasMoreData.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isProjectLoading.value = false;
        hasMoreData.value = false;
      },
    );
  }

  Future<void> callApiForFollowUser(
      {required BuildContext context,int? userID,bool isFollower = false}) {
    FocusScope.of(context).unfocus();
    final ApiModel updatePost = ApiModel("/friends/follow/user/${isFollower ? userID : userId.value}", APIType.POST);
    isFollowUnfollow.value = true;
    return apiManager.callApi(
      updatePost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            isFollowUnfollow.value = true;
            isFollowing.value = true;
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
          }
        } catch (error) {
          isFollowUnfollow.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isFollowUnfollow.value = false;
      },
    );
  }

  Future<void> callApiForUnFollowUser(
      {required BuildContext context}) {
    FocusScope.of(context).unfocus();
    final ApiModel updatePost = ApiModel("/friends/user/${userId.value}", APIType.DELETE);
    isFollowing.value = false;
    return apiManager.callApi(
      updatePost,
      params: {"unfollow": 1},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            isFollowing.value = false;
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
          }
        } catch (error) {
          isFollowing.value = true;
        }
      },
      failureCallback: (message, statusCode) {
        isFollowing.value = true;
      },
    );
  }

  Future<void> callApiForUnFollowerUser(
      {required BuildContext context,
        required int? userId,
        required bool isUnFollowed,
        required int index}) {
    FocusScope.of(context).unfocus();
    final ApiModel updatePost =
    ApiModel("/friends/user/$userId", APIType.DELETE);
    isFollowUnfollow.value = false;
    return apiManager.callApi(
      updatePost,
      params: {if (isUnFollowed) "unfollow": 1},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            isFollowUnfollow.value = false;
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
            if (!isUnFollowed) {
              followersList.removeAt(index);
              Navigator.pop(context);
            } else {
              followersList[index].followingUser?.isFollowing?.value = false;
              Navigator.pop(context);
            }
          }
        } catch (error) {
          print("Error in callApiForUnFollowerUser: $error");
          isFollowUnfollow.value = true;
        }
      },
      failureCallback: (message, statusCode) {
        print("Error in callApiForUnFollowerUser: $statusCode");
        print("Error in callApiForUnFollowerUser message: $message");
        isFollowUnfollow.value = true;
      },
    );
  }

  callApiForFollowingData({required BuildContext context}) {
    if (searchController.value.text.isNotEmpty) {
      // Cancel previous timer if exists
      debounceTimer?.cancel();

      // Start new debounce timer (500ms delay) for searching
      debounceTimer = Timer(Duration(milliseconds: 500), () {
        fetchFollowingData(context);
      });
    } else {
      fetchFollowingData(context);
    }
  }

  void fetchFollowingData(BuildContext context) {
    isLoading.value = true;

    apiManager.callApi(
      APIS.followers.getFollowings,
      params: {
        "UserId": userId.value,
        if (searchController.value.text.isNotEmpty)
          "searchQuery": searchController.value.text,
      },
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            FollowersResponse followingResponse =
            FollowersResponse.fromJson(response, isFollower: false);
            followersList.assignAll(followingResponse.data?.data ?? []);
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForGetUser(
      {required BuildContext context, String? userId}) {
    FocusScope.of(context).unfocus();
    final ApiModel updatePost = ApiModel("/users/$userId", APIType.GET);
    isProfileLoading.value = true;

    return apiManager.callApi(
      updatePost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            OtherUserModel otherUserModel = OtherUserModel.fromJson(response);
            userList.value = otherUserModel.data!;
            if(response["data"]["isFollowed"] == "1") {
              isFollowing.value = true;
            } else {
              isFollowing.value = false;
            }
            isProfileLoading.value = false;
          }
        } catch (error) {
          isProfileLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isProfileLoading.value = false;
        if(statusCode.contains("Cannot read properties of null")) {
          isProfileNotFound.value = true;
          print("isProfileNotFound == $isProfileNotFound");
        }
        print("statusCode === $statusCode");
        print("message === $message");
      },
    );
  }

  callApiForUserGetPost(
      {required BuildContext context,String? userId}) {
    FocusScope.of(context).unfocus();
    isGetPostLoading.value = true;
    // String? lastCreatedAt = postDataList.isNotEmpty
    //     ? postDataList.last.createdAt
    //     ?.toIso8601String() // Convert DateTime to String
    //     : null;

    return apiManager.callApi(
      APIS.post.getAllUsePost,
      params: {
        "limit": limit,
        "page": page.value,
        "UserId": userId,
      },
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            PostResponse postResponse = PostResponse.fromJson(response);
            if (postResponse.data.data.length < limit) {
              hasMoreData.value = false;
            }
            if (postResponse.data.data.isNotEmpty) {
              final existingIds = postDataList.map((e) => e.id).toSet();

              final newItems = postResponse.data.data.where((item) => !existingIds.contains(item.id));

              postDataList.addAll(newItems);
            } else {
              isGetPostLoading.value = false;
            }
            page.value++;
            log("response === $response");
          }
        } catch (error) {
          hasMoreData.value = false;
          isGetPostLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        hasMoreData.value = false;
        isGetPostLoading.value = false;
      },
    );
  }

  callApiForBookMarkProject(
      {required BuildContext context,
        String? postId,
        required int index}) {
    FocusScope.of(context).unfocus();
    postDataList[index].isBookMarked?.value =
    !(postDataList[index].isBookMarked?.value ?? false);
    final ApiModel getPost = ApiModel("/bookmarks/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            // postDataList[index].isBookMarked = response["post"]["isBookMarked"] == "0" ? false : true;
          }
        } catch (error) {
        }
      },
      failureCallback: (message, statusCode) {
      },
    );
  }

  callApiForLikeProject(
      {required BuildContext context,
        String? postId,
        required int index}) {
    FocusScope.of(context).unfocus();
    postDataList[index].isLiked?.value =
    !(postDataList[index].isLiked?.value ?? false);
    if (postDataList[index].isLiked?.value == true) {
      postDataList[index].likesCount?.value++;
    } else {
      postDataList[index].likesCount?.value--;
    }

    final ApiModel getPost = ApiModel("/post-likes/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
          }
        } catch (error) {
        }
      },
      failureCallback: (message, statusCode) {
      },
    );
  }

  callApiForCommentProject(
      {required BuildContext context, String? postId, required int index}) {
    FocusScope.of(context).unfocus();
    final ApiModel getPost =
    ApiModel("/post-comments/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      params: {"comment": commentController.value.text.trim()},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");

            isLoading.value = false;

            Comment newComment;

            if (response['data'] != null) {
              newComment = Comment.fromJson(response['data']);
            } else {
              newComment = Comment(
                id: DateTime.now().millisecondsSinceEpoch, // Temporary ID
                comment: commentController.value.text.trim(),
                createdAt: DateTime.now(),
                user: CurrentUser.user,
              );
            }

            List<Comment> updatedComments = [newComment, ...commentDataList];
            commentDataList.value = updatedComments;

            postDataList[index].commentsCount?.value = commentDataList.length;
          }
        } catch (error) {
          log("Error in callApiForCommentProject: $error");
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForGetCommentProject(
      {required BuildContext context, String? postId, required int index}) {
    FocusScope.of(context).unfocus();
    final ApiModel getPost =
    ApiModel("/post-comments/post/$postId", APIType.GET);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommentResponse commentResponse =
            CommentResponse.fromJson(response);
            commentDataList.value = commentResponse.data.data;
            postDataList[index].commentsCount?.value = commentDataList.length;
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForGetHighlightsOfOtherUser(
      {required BuildContext context}) async {

    try {
      await apiManager.callApi(
        CommonFunction.prepareApi(APIS.story.getHighlights, {'id' : ''}),
        params: {'UserId': userId.value},
        successCallback: (response, message) async {
          try {
            AllHighLightsModel allHighLightsModel =
            AllHighLightsModel.fromJson(response);
            highlights.value =
                allHighLightsModel.data?.highlights ?? [];
            // List<Future> highlightDetailsFutures = [];
            //
            // for (Highlight highlight in highlightsList) {
            //   if (highlight.id != null) {
            //     highlightDetailsFutures.add(_getHighlightDetails(highlight.id!)
            //         .then((detailedHighlight) {
            //       if (detailedHighlight != null) {
            //         int index = highlightsList
            //             .indexWhere((h) => h.id == detailedHighlight.id);
            //         if (index != -1) {
            //           highlightsList[index] = detailedHighlight;
            //         }
            //       }
            //     }));
            //   }
            // }
            //
            // await Future.wait(highlightDetailsFutures);

            // highlights.value = highlightsList;
            isLoading.value = false;
          } catch (error) {
            isLoading.value = false;
          }
        },
        failureCallback: (message, statusCode) {
          isLoading.value = false;
        },
      );
    } catch (e) {
      isLoading.value = false;
    }
  }

  // Future<Highlight?> _getHighlightDetails(int highlightId) async {
  //   Completer<Highlight?> completer = Completer();
  //
  //   apiManager.callApi(
  //     CommonFunction.prepareApi(
  //         APIS.story.getHighlights, {'id': highlightId.toString()}),
  //     successCallback: (response, message) {
  //       try {
  //         if (response != null &&
  //             response['status'] == 'success' &&
  //             response['data'] != null) {
  //           Highlight detailedHighlight = Highlight.fromJson(response['data']);
  //           completer.complete(detailedHighlight);
  //         } else {
  //           completer.complete(null);
  //         }
  //       } catch (error) {
  //         completer.complete(null);
  //       }
  //     },
  //     failureCallback: (message, statusCode) {
  //       completer.complete(null);
  //     },
  //   );
  //
  //   return completer.future;
  // }

  callApiForGetPostLike({required BuildContext context,required int postId}) async {
    isLoading.value = true;
    FocusScope.of(context).unfocus();
    String? lastCreatedAt = postLikeList.isNotEmpty
        ? postLikeList.last.createdAt?.toIso8601String()
        : null;

    try {
      await apiManager.callApi(
        ApiModel("/post-likes/post/$postId", APIType.GET),
        params: {"limit": 20, if (lastCreatedAt != null) "createdAt": lastCreatedAt},
        successCallback: (response, message) async {
          if (response['status'] == 'success') {
            PostLike postLike = PostLike.fromJson(response);
            if ((postLike.data?.data?.length ?? 0) < 20) {
              hasMoreData.value = false;
            }
            if ((postLike.data?.data ?? []).isNotEmpty) {
              postLikeList.addAll(postLike.data?.data ?? []);
              postLikeList.refresh();
            }
          }
          isLoading.value = false;
        },
        failureCallback: (message, statusCode) {
          hasMoreData.value = false;
          isLoading.value = false;

        },
      );
    } catch (error) {
      hasMoreData.value = false;
      isLoading.value = false;

    }
  }
}
