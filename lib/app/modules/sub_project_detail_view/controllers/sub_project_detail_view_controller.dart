import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/sub_project_detail_view/components/sub_project_section/sub_draft_section.dart';
import 'package:incenti_ai/app/modules/sub_project_detail_view/components/sub_project_section/sub_team_member_section.dart';

import '../../../../constants/api.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/constant.dart';
import '../../../../models/app_comment_model.dart';
import '../../../../models/app_post_model.dart';
import '../../../../models/app_project_model.dart';
import '../../../../models/app_todo_model.dart';
import '../../../../models/get_all_highlights_model.dart';
import '../../../../models/app_project_team_member_model.dart';
import '../../../../models/post_like_model.dart';
import '../../../../models/section_base.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/current_user.dart';
import '../components/sub_project_section/sub_file_section.dart';
import '../components/sub_project_section/sub_post_section.dart';
import '../components/sub_project_section/sub_todo_section.dart';

class SubProjectDetailViewController extends GetxController with WidgetsBindingObserver {
  RxBool isLoading = false.obs;
  RxBool isHighlightLoading = false.obs;
  List<SectionBase> sections = [];
  RxBool isUserLoading = false.obs;
  RxBool isFoldersLoading = false.obs;
  RxBool isProjectNotFound = false.obs;
  TextEditingController folderNameController = TextEditingController();
  ApiManager apiManager = ApiManager();
  Rx<Project> getProjectDetailData = Project().obs;
  final ScrollController scrollController = ScrollController();
  var args = Get.arguments;
  RxInt currentSelectedIndex = 0.obs;
  RxList<Post> postDataList = <Post>[].obs;
  RxList<FolderWrapper> folderList = <FolderWrapper>[].obs;
  RxList<SubProjectData> subProjectList = <SubProjectData>[].obs;
  int limit = 5;
  RxBool isShare = false.obs;
  RxBool hasMoreData = true.obs;
  RxInt projectId = (-1).obs;
  RxList<Comment> commentDataList = <Comment>[].obs;
  TextEditingController commentController = TextEditingController();
  FocusNode commentFocusNode = FocusNode();
  RxList<Highlight> highlights = <Highlight>[].obs;
  RxList<ProjectTeamMemberData> teamMemberDataList =
      <ProjectTeamMemberData>[].obs;
  RxInt selectedIndex = 0.obs;
  RxList<PostLikeData> postLikeList = <PostLikeData>[].obs;
  RxList<ToDo> todoList = <ToDo>[].obs;
  final downloadingFileIds = <int>{}.obs;
  RxInt page = 1.obs;
  RxString selectedReason = "".obs;
  RxList repostData = [
    "I just don't Like it",
    "Scam, fraud or spam",
    "False information",
  ].obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    print("onInit called ===>");
    if (args != null && args["projectId"] != null) {
      projectId.value = args["projectId"];
      callApiForGetOneProject(
              context: Get.context!, projectId: args["projectId"].toString())
          .then(
        (value) {
          callApiForUserGetPost(context: Get.context!);
          callApiForGetHighlightsOfSubProject(context: Get.context!);
          if (CurrentUser.user.id == getProjectDetailData.value.userData?.id ||
              CurrentUser.user.id == getProjectDetailData.value.parentProjectData?.userId ||
              getProjectDetailData.value.projectMembers.isNotEmpty) {
            sections = [
              PostsSection(controller: this),
              TodoSection(controller: this),
              FileSection(controller: this),
              TeamMemberSection(controller: this),
              DraftSection(controller: this),
            ];
          } else {
            sections = [
              PostsSection(controller: this),
            ];
          }
        },
      );
    }
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if(isShare.value) {
      if (state == AppLifecycleState.resumed) {
        isShare.value = false;
        CommonFunction.showCustomSnackbar(
            message: "The sub-project has been shared successfully");
      }
      return;
    }
  }
  callApiForReportProject(
      {required BuildContext context, required String projectId}) {
    FocusScope.of(context).unfocus();
    // isLoading.value = true;
    final ApiModel updatePost =
        ApiModel("/reported-projects/project/$projectId", APIType.POST);
    log("selected ==> ${selectedReason.value}");
    return apiManager.callApi(
      updatePost,
      params: {"reason": selectedReason.value},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            Navigator.pop(context);
            CommonFunction.showCustomSnackbar(message: response['message']);
          }
        } catch (error) {}
      },
      failureCallback: (message, statusCode) {},
    );
  }

  callApiForGetPostLike({required BuildContext context,required int postId}) async {
    isLoading.value = true;
    FocusScope.of(context).unfocus();
    String? lastCreatedAt = postLikeList.isNotEmpty
        ? postLikeList.last.createdAt?.toIso8601String()
        : null;

    try {
      await apiManager.callApi(
        ApiModel("/post-likes/post/$postId", APIType.GET),
        params: {"limit": 20, if (lastCreatedAt != null) "createdAt": lastCreatedAt},
        successCallback: (response, message) async {
          if (response['status'] == 'success') {
            PostLike postLike = PostLike.fromJson(response);
            if ((postLike.data?.data?.length ?? 0) < 20) {
              hasMoreData.value = false;
            }
            if ((postLike.data?.data ?? []).isNotEmpty) {
              postLikeList.addAll(postLike.data?.data ?? []);
              postLikeList.refresh();
            }
          }
          isLoading.value = false;
        },
        failureCallback: (message, statusCode) {
          hasMoreData.value = false;
          isLoading.value = false;

        },
      );
    } catch (error) {
      hasMoreData.value = false;
      isLoading.value = false;

    }
  }
  Future<void> callApiForDeleteTeamMember(
      {required BuildContext context, required int? teamId,bool isLeaveTeam = false,required int userId}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel updatePost =
        ApiModel("/project-members/$teamId/$userId", APIType.DELETE);
    return apiManager.callApi(
      updatePost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            isLoading.value = false;
            if(isLeaveTeam) {
              CommonFunction.showCustomSnackbar(
                message: "Sub-project left successfully",
              );
            } else {
              CommonFunction.showCustomSnackbar(
                message: response['message'],
              );
            }
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForCompleteToDo(
      {required BuildContext context,
      required String todoId,
      required int index}) {
    FocusScope.of(context).unfocus();
    todoList[index].status?.value = !(todoList[index].status?.value ?? false);
    final ApiModel getPost = ApiModel("/todos/status/$todoId", APIType.PATCH);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
          }
        } catch (error) {
          log("error === $error");
        }
      },
      failureCallback: (message, statusCode) {
        CommonFunction.showCustomSnackbar(
            message: statusCode, isError: true, backgroundColor: AppTheme.red);
        todoList[index].status?.value =
            !(todoList[index].status?.value ?? false);
      },
    );
  }

  Future<void> callApiForDeleteOneToDo(
      {required BuildContext context, String? todoId}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost = ApiModel("/todos/$todoId", APIType.DELETE);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            Navigator.pop(context);
            CommonFunction.showCustomSnackbar(message: response['message']);
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForToDo({required BuildContext context}) async {
    FocusScope.of(context).unfocus();
    isLoading.value = true;

    try {
      Map<String, dynamic> dict = {
        "limit": limit,
        "page": page.value,
        "ProjectIds": projectId.value
      };
      log("dict==$dict");
      await apiManager.callApi(
        APIS.todo.getTodo,
        params: dict,
        successCallback: (response, message) async {
          if (response['status'] == 'success') {
            TodoResponse todoResponse = TodoResponse.fromJson(response);
            if (todoResponse.data.data.length < limit) {
              hasMoreData.value = false;
            }
            if (page.value == 1) {
              todoList.clear();
            }
            todoList.addAll(todoResponse.data.data);
            page++;
          }
        },
        failureCallback: (message, statusCode) {
          hasMoreData.value = false;
          isLoading.value = false;
        },
      );
    } catch (error) {
      hasMoreData.value = false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> callApiForGetSubProjectTeamMember(
      {required BuildContext context}) {
    isLoading.value = true;
    final ApiModel getProjectMember =
        ApiModel("/project-members/projects/$projectId", APIType.GET);

    return apiManager.callApi(
      getProjectMember,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            // Parse the response and update the list
            ProjectTeamMemberResponse parsedResponse =
                ProjectTeamMemberResponse.fromJson(response);
            if (parsedResponse.data.length < limit) {
              hasMoreData.value = false;
            }
            teamMemberDataList.value = parsedResponse.data;
            isLoading.value = false;
          }
        } catch (error) {
          hasMoreData.value = false;
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        hasMoreData.value = false;
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForProjectMemberRole(
      {required BuildContext context,
      String? projectId,
      int index = 0,
      required String access}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost =
        ApiModel("/project-members/$projectId", APIType.PATCH);

    return apiManager.callApi(
      getPost,
      params: {"access": access},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            //  else {
            //   getProjectDetailData.value.hide =
            //   !(getProjectDetailData.value.hide ?? false);
            // }
            Get.back();
            CommonFunction.showCustomSnackbar(message: response['message']);
            log("response === $response");
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForHideProject({
    required BuildContext context,
    String? projectId,
    required bool isHide,
  }) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost = ApiModel("/projects/$projectId", APIType.PATCH);

    return apiManager.callApi(
      getPost,
      params: {"hide": isHide},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            getProjectDetailData.value.hide =
                !(getProjectDetailData.value.hide ?? false);

            Get.back();
            if(getProjectDetailData.value.hide ==
                true) {
              CommonFunction.showCustomSnackbar(message: "Other users won't be able to view this Private Project Title and Cover Image");
            } else {
              CommonFunction.showCustomSnackbar(message: "Other Users are now able to view your Project Title and Cover Image");
            }
            log("response === $response");
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForGetOneProject(
      {required BuildContext context, required String projectId}) {
    FocusScope.of(context).unfocus();
    isUserLoading.value = true;
    final ApiModel getPost = ApiModel("/projects/$projectId", APIType.GET);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            getProjectDetailData.value = Project.fromJson(response["data"]);
            isUserLoading.value = false;
          }
        } catch (error) {
          isUserLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        if(statusCode == "Project not found") {
          isProjectNotFound.value = true;
        }
        isUserLoading.value = false;
      },
    );
  }

  List storyList = [
    {"image": AppImage.story1, "name": "Photography"},
    {"image": AppImage.story2, "name": "Fitness"},
    {"image": AppImage.story3, "name": "Healthy Diet "},
    {"image": AppImage.story1, "name": "Photography"},
  ];
  final iconMap = {
    1: AppImage.todosIcon,
    2: AppImage.projectIcon,
    3: AppImage.invite,
  };
  List userProjectList = ["Posts", "To Do", "Files", "Members", "Drafts"];

  Future<void> callApiForOneSubProject(
      {required BuildContext context, String? projectId, int? userId}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;

    return apiManager.callApi(
      APIS.project.getAllMyProject,
      params: {"UserId": userId, "ParentId": projectId},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            subProjectList.value = List<SubProjectData>.from(response["data"]
                    ["data"]
                .map((x) => SubProjectData.fromJson(x)));
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForDeleteOnePost(
      {required BuildContext context, int? postId, bool isDraft = false}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost = ApiModel("/posts/$postId", APIType.DELETE);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            Get.back();
            // Clear the list and refresh it
            postDataList.clear();
            update();
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForUserGetPost(
      {required BuildContext context, bool isPublished = false}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    // String? lastCreatedAt = postDataList.isNotEmpty
    //     ? postDataList.last.createdAt
    //         ?.toIso8601String() // Convert DateTime to String
    //     : null;

    return apiManager.callApi(
      APIS.post.getAllUsePost,
      params: {
        "limit": limit,
        "page": page.value,
        // "UserId": getProjectDetailData.value.userData?.id,
        "ProjectId": projectId.value,
        if (isPublished) "isPublished": 0
      },
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            PostResponse postResponse = PostResponse.fromJson(response);
            if (postResponse.data.data.length < limit) {
              hasMoreData.value = false;
            }
            if (postResponse.data.data.isNotEmpty) {
              postDataList.addAll(postResponse.data.data);
              isLoading.value = false;
            } else {
              return;
            }
            page.value++;
            log("response === $response");
          }
        } catch (error) {
          isLoading.value = false;
          hasMoreData.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
        hasMoreData.value = false;
      },
    );
  }

  callApiForLikeProject(
      {required BuildContext context, String? postId, required int index}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    postDataList[index].isLiked?.value =
        !(postDataList[index].isLiked?.value ?? false);
    if (postDataList[index].isLiked?.value == true) {
      postDataList[index].likesCount?.value++;
    } else {
      postDataList[index].likesCount?.value--;
    }
    final ApiModel getPost = ApiModel("/post-likes/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            // postDataList[index].isLiked = (response["post"]["isLiked"] == "0" ? false : true).obs;
            // postDataList[index].likesCount = int.parse(response["post"]["likesCount"] ?? "0");
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForGetCommentProject(
      {required BuildContext context, String? postId, required int index}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost =
        ApiModel("/post-comments/post/$postId", APIType.GET);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommentResponse commentResponse =
                CommentResponse.fromJson(response);
            commentDataList.value = commentResponse.data.data;
            postDataList[index].commentsCount?.value = commentDataList.length;
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForCommentProject(
      {required BuildContext context, String? postId, required int index}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost =
        ApiModel("/post-comments/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      params: {"comment": commentController.value.text.trim()},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");

            isLoading.value = false;

            Comment newComment;

            if (response['data'] != null) {
              newComment = Comment.fromJson(response['data']);
            } else {
              newComment = Comment(
                id: DateTime.now().millisecondsSinceEpoch, // Temporary ID
                comment: commentController.value.text.trim(),
                createdAt: DateTime.now(),
                user: CurrentUser.user,
              );
            }

            List<Comment> updatedComments = [newComment, ...commentDataList];
            commentDataList.value = updatedComments;

            postDataList[index].commentsCount?.value = commentDataList.length;
          }
        } catch (error) {
          log("Error in callApiForCommentProject: $error");
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForBookMarkProject(
      {required BuildContext context, String? postId, required int index}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    postDataList[index].isBookMarked?.value =
        !(postDataList[index].isBookMarked?.value ?? false);
    final ApiModel getPost = ApiModel("/bookmarks/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            // postDataList[index].isBookMarked = response["post"]["isBookMarked"] == "0" ? false : true;
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForGetHighlightsOfSubProject(
      {required BuildContext context}) async {
    isHighlightLoading.value = true;
    try {
      await apiManager.callApi(
        CommonFunction.prepareApi(APIS.story.getHighlights, {'id': ''}),
        params: {
          'ProjectId': projectId.value,
          'UserId': getProjectDetailData.value.userId
        },
        successCallback: (response, message) async {
          try {
            AllHighLightsModel allHighLightsModel =
                AllHighLightsModel.fromJson(response);
            highlights.value =
                allHighLightsModel.data?.highlights ?? [];
            // List<Future> highlightDetailsFutures = [];
            //
            // for (Highlight highlight in highlightsList) {
            //   if (highlight.id != null) {
            //     highlightDetailsFutures.add(_getHighlightDetails(highlight.id!)
            //         .then((detailedHighlight) {
            //       if (detailedHighlight != null) {
            //         int index = highlightsList
            //             .indexWhere((h) => h.id == detailedHighlight.id);
            //         if (index != -1) {
            //           highlightsList[index] = detailedHighlight;
            //         }
            //       }
            //     }));
            //   }
            // }
            //
            // await Future.wait(highlightDetailsFutures);

            // highlights.value = highlightsList;
            isHighlightLoading.value = false;
          } catch (error) {
            isHighlightLoading.value = false;
          }
        },
        failureCallback: (message, statusCode) {
          isHighlightLoading.value = false;
        },
      );
    } catch (e) {
      isHighlightLoading.value = false;
    }
  }

  // Future<Highlight?> _getHighlightDetails(int highlightId) async {
  //   Completer<Highlight?> completer = Completer();
  //
  //   apiManager.callApi(
  //     CommonFunction.prepareApi(
  //         APIS.story.getHighlights, {'id': highlightId.toString()}),
  //     successCallback: (response, message) {
  //       try {
  //         if (response != null &&
  //             response['status'] == 'success' &&
  //             response['data'] != null) {
  //           Highlight detailedHighlight = Highlight.fromJson(response['data']);
  //
  //           completer.complete(detailedHighlight);
  //         } else {
  //           completer.complete(null);
  //         }
  //       } catch (error) {
  //         completer.complete(null);
  //       }
  //     },
  //     failureCallback: (message, statusCode) {
  //       completer.complete(null);
  //     },
  //   );
  //   return completer.future;
  // }

  Future<void> callApiForGetFolders({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    isFoldersLoading.value = true;
    log('api called for =====> get folders');

    return apiManager.callApi(
      APIS.folders.getAllFolders,
      params: {"ProjectId": projectId},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            folderList.value = List<ProjectFolder>.from(
                response["data"]["data"].map((x) => ProjectFolder.fromJson(x)));
            isFoldersLoading.value = false;
          }
        } catch (error) {
          isFoldersLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isFoldersLoading.value = false;
      },
    );
  }

  Future<void> callApiForCreateFolder({
    required BuildContext context,
    required String folderName,
  }) async {
    log('api called for =====> create folder');

    FocusScope.of(context).unfocus();
    Get.back();
    folderNameController.clear();
    isFoldersLoading.value = true;
    apiManager.callApi(
      APIS.folders.createFolder,
      params: {
        'ProjectId': projectId.toString(),
        'name': folderName,
      },
      successCallback: (response, message) {
        callApiForGetFolders(
          context: Get.context!,
        );
      },
      failureCallback: (message, statusCode) {
        CommonFunction.showCustomSnackbar(
          message: statusCode,
          isError: true,
          backgroundColor: AppTheme.red,
        );
        isFoldersLoading.value = false;
      },
      showErrorDialog: false,
    );
  }

  Future<void> callApiForEditFolder({
    required BuildContext context,
    int? folderId,
    required String folderName,
  }) async {
    log('api called for =====> edit folder');

    FocusScope.of(context).unfocus();
    Get.back();
    folderNameController.clear();
    isFoldersLoading.value = true;
    apiManager.callApi(
      CommonFunction.prepareApi(APIS.folders.editFolder, {
        'id': folderId.toString(),
      }),
      params: {
        'name': folderName,
      },
      successCallback: (response, message) {
        callApiForGetFolders(
          context: Get.context!,
        );
      },
      failureCallback: (message, statusCode) {
        CommonFunction.showCustomSnackbar(
          message: statusCode,
          isError: true,
          backgroundColor: AppTheme.red,
        );
        isFoldersLoading.value = false;
      },
      showErrorDialog: false,
    );
  }

  Future<void> callApiForDeleteFolder({
    required int folderId,
  }) async {
    log('api called for =====> delete one folder');

    Get.back();
    isFoldersLoading.value = true;
    apiManager.callApi(
      CommonFunction.prepareApi(
        APIS.folders.deleteFolder,
        {'id': folderId.toString()},
      ),
      successCallback: (response, message) {
        callApiForGetFolders(
          context: Get.context!,
        );
      },
      failureCallback: (message, statusCode) {},
    );
  }
}
